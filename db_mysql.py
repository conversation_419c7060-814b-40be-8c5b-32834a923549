import datetime

import pymysql


class MySQLDatabase:
    def __init__(self):
        self.G_DB_Arr = {
            "host": "**************",
            "port": 3306,
            "user": "tracking",
            "password": "GdrBr57zHF7fJ6Ci",
            "charset": "utf8mb4",
            "database": "tracking",
        }
        self.dbh = None
        self.History = []

    # MySQL connection
    def connection(self):
        try:
            self.dbh = pymysql.connect(
                host=self.G_DB_Arr["host"],
                port=self.G_DB_Arr["port"],
                user=self.G_DB_Arr["user"],
                password=self.G_DB_Arr["password"],
                database=self.G_DB_Arr["database"],
                charset=self.G_DB_Arr["charset"],
            )
            return self.dbh
        except pymysql.MySQLError as e:
            return {"code": 0, "msg": "Connection failed", "data": []}

    # Create table
    def create_table(self, table):
        sql = f"""
        CREATE TABLE {table} (
            cache_key VARCHAR(255) PRIMARY KEY,
            cache_value TEXT NOT NULL,
            expiration_time DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );
        """
        with self.dbh.cursor() as cursor:
            cursor.execute(sql)
        self.dbh.commit()

    # Set cache
    def set_cache(self, table, key, value, expiration_time=None):
        self.History.append(f"set-{table}-{key}")
        if expiration_time is None:
            expiration_time = (
                datetime.datetime.now() + datetime.timedelta(days=1)
            ).strftime("%Y-%m-%d %H:%M:%S")

        try:
            with self.dbh.cursor() as cursor:
                sql = f"SELECT cache_key FROM {table} WHERE cache_key = %s"
                cursor.execute(sql, (key,))
                row = cursor.fetchone()

                if row:
                    sql = f"UPDATE {table} SET cache_value = %s, expiration_time = %s WHERE cache_key = %s"
                    cursor.execute(sql, (value, expiration_time, key))
                else:
                    sql = f"INSERT INTO {table} (cache_key, cache_value, expiration_time) VALUES (%s, %s, %s)"
                    cursor.execute(sql, (key, value, expiration_time))
                self.dbh.commit()
            return {"code": 1, "data": None, "msg": "Cache operation successful."}
        except pymysql.MySQLError as e:
            return {"code": 0, "data": None, "msg": f"Database error: {e}"}

    # set Cache list
    # example data = {'key1': ('value1', '2021-12-31 23:59:59'), 'key2': ('value2', '2022-01-01 23:59:59')}
    def set_cache_list(self, table, data):
        self.History.append(f"set-{table}")
        if self.dbh is None:
            return {"code": 0, "data": None, "msg": "No database connection."}
        try:
            with self.dbh.cursor() as cursor:
                insert_data = []
                for key, (value, expiration_time) in data.items():
                    insert_data.append((key, value, expiration_time))

                sql = f"INSERT INTO {table} (cache_key, cache_value, expiration_time) VALUES (%s, %s, %s) ON DUPLICATE KEY UPDATE cache_value=VALUES(cache_value), expiration_time=VALUES(expiration_time)"
                cursor.executemany(sql, insert_data)
                self.dbh.commit()
            return {"code": 1, "data": None, "msg": "Cache operation successful."}
        except pymysql.MySQLError as e:
            return {"code": 0, "data": None, "msg": f"Database error: {e}"}

    # Get cache
    def get_cache(self, table, key):
        self.History.append(f"get-{table}-{key}")
        sql = f"SELECT cache_value, expiration_time FROM {table} WHERE cache_key = %s AND (expiration_time IS NULL OR expiration_time > NOW())"
        with self.dbh.cursor() as cursor:
            cursor.execute(sql, (key,))
            row = cursor.fetchone()

        if row:
            return {"code": 1, "data": row[0], "msg": "Cache fetched successfully."}
        else:
            return {"code": 0, "data": None, "msg": "Cache not found or expired."}

    # Delete cache
    def delete_cache(self, table, key):
        sql = f"DELETE FROM {table} WHERE cache_key = %s"
        with self.dbh.cursor() as cursor:
            cursor.execute(sql, (key,))
            self.dbh.commit()

        if cursor.rowcount > 0:
            return {"code": 1, "data": None, "msg": "Cache deleted successfully."}
        else:
            return {"code": 0, "data": None, "msg": "Cache not found."}

    # Get history
    def get_history(self):
        return self.History
