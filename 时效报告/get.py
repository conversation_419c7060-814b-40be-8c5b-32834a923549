import datetime
import json
import os
import re
import sqlite3

from clients.oa import OAClient
from clients.tla import AUSOAClient


def get_data():

    # 检查数据库文件是否存在
    db_file = "getData.db"
    db_exists = os.path.exists(db_file)

    # 连接数据库
    conn = sqlite3.connect(db_file)
    c = conn.cursor()

    # 如果数据库不存在,创建表
    if not db_exists:
        c.execute(
            """CREATE TABLE IF NOT EXISTS shipment
                    (ref TEXT PRIMARY KEY, 
                    CN_In TEXT,
                    Cleared TEXT,
                    ATD TEXT,
                    ETD TEXT,
                    Delivered TEXT,
                    ChannelCode TEXT,
                    CountryCodeE TEXT,
                    ShipmentDetail TEXT,
                    ShipmentDetailCN TEXT,
                    FBAId TEXT,
                    TracksAUS TEXT,
                    Tracks TEXT)"""
        )

        # 读取单号并去重
        with open("connote.txt", "r") as f:
            connote_list = list(set(line.strip() for line in f))

        # 批量插入数据,每200条执行一次
        for i in range(0, len(connote_list), 200):
            batch = [(ref,) for ref in connote_list[i : i + 200]]
            c.executemany("INSERT INTO shipment (ref) VALUES (?)", batch)
            conn.commit()
    else:
        # 读取未清关的数据
        c.execute("SELECT ref FROM shipment WHERE Cleared IS NULL OR Cleared = ''")
        connote_list = [row[0] for row in c.fetchall()]

    print(f"共 {len(connote_list)} 个单号需要处理")

    # # 处理本地数据 读取 dbData.json
    # if os.path.exists("dbData.json"):
    #     with open("dbData.json", "r") as f:
    #         db_data = json.load(f)
    #         # 保存数据
    #         for item in db_data.get("data"):

    #             # {"billid": "WEB25071630", "transbillid": "SKP4534185061", "indate": "2025-07-07 19:33:06.000", "rchannelid": "SC04", "country": "BWU2", "rrweight": "32.850", "rmcal": "6000", "signid": null, "rmweight": "36.010", "goodsnum": "2", "clientname": "飞池国际", "transport_mode": "air"}
    #             c.execute(
    #                 "UPDATE shipment SET ShipmentDetailCN = ?, CN_In = ?, ChannelCode = ?, CountryCodeE = ? WHERE ref = ?",
    #                 (
    #                     json.dumps(item),
    #                     item["indate"],
    #                     item["transport_mode"],
    #                     item["country"],
    #                     item["transbillid"],
    #                 ),
    #             )

    # # 获取数据
    # client = AUSOAClient()
    # results = client.AUSOAGetShipmentDetail(connote_list)

    # # 保存数据
    # if results.get("code") == 200:
    #     for item in results.get("data"):

    #         # 提取对应的 FBAId
    #         # 优先从 amazon_shipment_ids 获取
    #         fba_id = ""
    #         fba_id_text = ""

    #         # 拼接所有字段为一个字符串
    #         fba_id_text = f"{item.get('amazon_shipment_ids', '')} {item.get('amazon_po', '')} {item.get('delivery_booking_number', '')} {item.get('cust_ref', '')}"

    #         # 查找所有符合FBA格式的ID
    #         fba_matches = re.findall(r"FBA[A-Z0-9]{9}", fba_id_text)

    #         # 如果找到匹配项,取第一个
    #         if fba_matches:
    #             fba_id = fba_matches[0]

    #         # 保存数据到数据库,增加FBAId字段
    #         c.execute(
    #             "UPDATE shipment SET ShipmentDetail = ?, FBAId = ? WHERE ref = ?",
    #             (json.dumps(item), fba_id, item["ref"]),
    #         )

    # # 获取 client的轨迹
    # resultsTrack = client.AUSOATracking(connote_list)
    # # 存放数据
    # if resultsTrack.get("code") == 200:
    #     for item in resultsTrack.get("data"):
    #         # 从轨迹中获取清关时间
    #         cleared_date = ""
    #         # 确保item是字典类型
    #         if isinstance(item, str):
    #             item = json.loads(item)

    #         if item.get("tracks"):
    #             for track in item["tracks"]:
    #                 if track[3] == "Cleared":
    #                     cleared_date = track[2]
    #                     break

    #         c.execute(
    #             "UPDATE shipment SET TracksAUS = ?, Cleared = ? WHERE ref = ?",
    #             (json.dumps(item), cleared_date, item["ref"]),
    #         )

    # 获取轨迹数据 整合数据
    oa_client = OAClient()
    track_results = oa_client.getTracks(
        connote_list,
        "http://localhost.ptlogin2.qq.com:6608/scintl-openapi/api/index.php?ver=local&language=zh&debug=tss&OrderId=",
        10,
    )

    # 保存数据到本地
    with open("track_results.json", "w") as f:
        f.write(json.dumps(track_results, ensure_ascii=False))

    # 保存轨迹数据
    for ref, track_data in track_results.items():
        # trackData 是字符串，需要先解析为json
        # 检查是否存在trackData字段
        if "trackData" not in track_data:
            print(f"跳过处理 {ref}: 缺少trackData字段")
            continue

        track_data_json = json.loads(track_data["trackData"])
        track_data_obj = track_data_json["data"]

        # 从track_data中提取需要的数据
        CN_In = track_data_obj["StatusList"].get("CN In", "")
        Cleared = track_data_obj["StatusList"].get("Cleared", "")
        ChannelCode = track_data_obj.get("ChannelCode", "")
        CountryCodeE = track_data_obj.get("CountryCodeE", "")
        ATD = track_data_obj["StatusList"].get("ATD", "")
        ETD = track_data_obj["StatusList"].get("ETD", "")
        Delivered = track_data_obj["StatusList"].get("Delivered", "")

        print(CN_In, Cleared, ChannelCode, CountryCodeE)

        # 保存数据
        c.execute(
            """UPDATE shipment
            SET Tracks = ?, CN_In = ?, Cleared = ?, ATD = ?, ETD = ?, Delivered = ?, ChannelCode = ?, CountryCodeE = ?
            WHERE ref = ?""",
            (
                json.dumps(track_data, ensure_ascii=False),
                CN_In,
                Cleared,
                ATD,
                ETD,
                Delivered,
                ChannelCode,
                CountryCodeE,
                ref,
            ),
        )

    conn.commit()
    conn.close()


# 读取数据库导出 csv
# ref\CN_In\Cleared\ChannelCode\CountryCodeE\FBAId TEXT
def export_csv():
    # 连接数据库
    conn = sqlite3.connect("getData.db")
    c = conn.cursor()

    # 读取数据
    c.execute(
        """
        SELECT 
            ref, CN_In, Cleared, ATD, ETD, Delivered, ChannelCode, CountryCodeE, FBAId
        FROM shipment
        """
    )
    data = c.fetchall()
    # 保存到文件
    with open("output.csv", "w") as f:
        # 表头
        f.write("ref,CN_In,Cleared,ATD,ETD,Delivered,ChannelCode,CountryCodeE,FBAId\n")
        for item in data:
            # 将None值转换为空字符串,并处理日期格式
            processed_item = []
            for i, x in enumerate(item):
                if x is None:
                    processed_item.append("")
                elif i in (1, 2) and x:  # CN_In和Cleared的索引是1和2
                    try:
                        # 尝试不同的日期格式
                        date_formats = [
                            "%Y-%m-%d %H:%M:%S.%f",
                            "%Y-%m-%d %H:%M:%S",
                            "%Y-%m-%d %H:%M",
                            "%Y-%m-%d",
                            "%d/%m/%Y %H:%M:%S",
                        ]

                        for fmt in date_formats:
                            try:
                                date = datetime.datetime.strptime(x, fmt)
                                processed_item.append(date.strftime("%d/%m/%Y"))
                                break
                            except ValueError:
                                continue
                        else:
                            # 如果所有格式都失败了,保持原样
                            processed_item.append(x)
                    except:
                        processed_item.append(x)
                else:
                    processed_item.append(str(x))
            f.write(",".join(processed_item) + "\n")


# 运行
if __name__ == "__main__":
    get_data()
    export_csv()
