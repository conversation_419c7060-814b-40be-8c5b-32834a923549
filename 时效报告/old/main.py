import csv
import hashlib
import inspect
import json
import os
import re
import sqlite3
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta

import requests


def connect_sqlite():
    path = "./shixiao.db"
    sql = """
CREATE TABLE shipment (
    connote TEXT PRIMARY KEY,
    ref TEXT,
    cref TEXT,
    indate TEXT,
    channelid TEXT,
    packs INTEGER,
    weight REAL,
    cbm REAL,
    country TEXT,
    client TEXT,
    mode TEXT,
    trackData TEXT,
    T1 INTEGER,
    T2 INTEGER,
    T3 INTEGER,
    T4 INTEGER,
    TPT INTEGER,
    CarrierName TEXT,
    OriginPort TEXT,
    DestPort TEXT
);
    """

    # 如果不存在就创建
    if not os.path.exists(path):
        conn = sqlite3.connect(path)
        c = conn.cursor()
        c.execute(sql)
        conn.commit()
    else:
        conn = sqlite3.connect(path)
        c = conn.cursor()
    return c, conn


# 插入数据独立出来
def dbSave(connoteList):
    c, conn = connect_sqlite()

    # 剔除和数据库重复的 connote是主键唯一
    connoteList = [
        item
        for item in connoteList
        if c.execute("SELECT * FROM shipment WHERE connote=?", (item,)).fetchall() == []
    ]

    # 构建基础数据
    list = []
    for item in connoteList:
        list.append(
            {
                "connote": item,
                "ref": item,
                "indate": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
                "channelid": "",
                "packs": 0,
                "weight": 0,
                "cbm": 0,
                "country": "",
                "client": "",
                "mode": "",
            }
        )

    # 分批插入 每次最多200
    for i in range(0, len(list), 200):
        c.executemany(
            "INSERT INTO shipment (connote, ref, indate, channelid, packs, weight, cbm, country, client, mode) VALUES (?,?,?,?,?,?,?,?,?,?)",
            [
                (
                    item["connote"],
                    item["ref"],
                    item["indate"],
                    item["channelid"],
                    item["packs"],
                    item["weight"],
                    item["cbm"],
                    item["country"],
                    item["client"],
                    item["mode"],
                )
                for item in list[i : i + 200]
            ],
        )
        conn.commit()

    return len(list)


def getData(key, url, timeout):
    jsonData = {
        "key": key,
        # debug：SELECT TOP 1 PERCENT
        "sql": """
SELECT 
    e.billid, 
    e.transbillid,
    e.indate,
    e.rchannelid, 
    e.country,
    e.rrweight,
    e.rmcal,
    w.signid,
    e.rmweight,
    e.goodsnum,
    c.clientname,
    CASE 
        WHEN air_items.sheetid IS NOT NULL THEN 'air'
        ELSE 'sea'
    END AS transport_mode
FROM 
    p_exportItem e
LEFT JOIN 
    client c ON e.clientid = c.clientid
LEFT JOIN 
    t_user u ON c.salemanid = u.userid
LEFT JOIN 
    billstatus w ON e.billid = w.billid
LEFT JOIN 
    (SELECT sheetid FROM p_airItem) air_items ON e.sheetid = air_items.sheetid
WHERE 
    e.indate > '2023-01-01' 
    AND e.sheetid IN (
        SELECT sheetid
        FROM (
            SELECT sheetid, info
            FROM p_shipItem
            UNION ALL
            SELECT sheetid, info
            FROM p_airItem
        ) AS combined_results
    )
    AND w.signid = 200
    AND e.indate < '2026-01-01'
    AND (
        e.country = 'BWU1' 
        OR e.country = 'BWU2'
        OR e.country = 'BWU6'
        OR e.country = 'MEL1'
        OR e.country = 'MEL5'
        OR e.country = 'PER2'
        OR e.country = 'PER3'
        OR e.country = 'VANE'
        OR e.country = 'BNE1'
    )
ORDER BY 
    e.sheetid DESC
;
                    """,
    }

    res = requests.post(url, json=jsonData, timeout=timeout)

    if res.status_code != 200:
        # 直接报错 抛出 异常 让except处理
        print("请求失败")
        return None

    connoteArr = []

    if int(res.json()["code"]) == 200:
        print(f"-获取运单的列表成功~")
        # 获取请求的列表成功
        data = res.json()["data"]
        if len(data) <= 0:
            print("没有数据")
            return None

        for key, item in enumerate(data, start=1):
            # 不刷新的 进度条
            print(f"进度:{key}/{len(data)}", end="\r")
            connoteArr.append(
                {
                    "connote": item["billid"],
                    "ref": item["transbillid"],
                    "cref": "",
                    "indate": item["indate"],
                    "channelid": item["rchannelid"],
                    "country": item["country"],
                    "client": item["clientname"],
                    "mode": item["transport_mode"],
                    "packs": int(item["goodsnum"]),
                    "weight": float(item["rmweight"]),
                    "cbm": round(
                        float(item.get("rmweight", 0))
                        * float(item.get("rmcal", 0))
                        / 1000000,
                        3,
                    ),
                }
            )

    # 插入到数据库
    c, conn = connect_sqlite()

    # 剔除和数据库重复的 connote是主键唯一
    connoteArr = [
        item
        for item in connoteArr
        if c.execute(
            "SELECT * FROM shipment WHERE connote=?", (item["connote"],)
        ).fetchall()
        == []
    ]

    # 分批插入 每次最多200
    for i in range(0, len(connoteArr), 200):
        c.executemany(
            "INSERT INTO shipment (connote, ref, indate, channelid, packs, weight, cbm, country, client, mode) VALUES (?,?,?,?,?,?,?,?,?,?)",
            [
                (
                    item["connote"],
                    item["ref"],
                    datetime.strptime(item["indate"], "%Y-%m-%d %H:%M:%S.%f"),
                    item["channelid"],
                    item["packs"],
                    item["weight"],
                    item["cbm"],
                    item["country"],
                    item["client"],
                    item["mode"],
                )
                for item in connoteArr[i : i + 200]
            ],
        )
        conn.commit()

    print(f"-获取运单的列表成功~ 共{len(connoteArr)}个订单")
    # 转为json 保存到当前目录的temp
    with open("temp.json", "w") as f:
        f.write(json.dumps(connoteArr, ensure_ascii=False))


def getTracks(list, url, timeout):
    # 多线程请求结果

    # Url是  https://api.gz.quannanguoji.com/openapi/?ver=dalton&language=zh&key=tss&OrderId=AMELIA107
    # AMELIA107 是单号

    # 这里给的是list list的ref是单号

    max_thread = 20
    results = {}

    # 多线程写入保护
    save_lock = threading.Lock()

    # 请求
    def get_track(index, ref):
        print("请求中" + str(ref))

        # ref 只保留 字母和数字-_符号
        ref = re.sub(r"[^\w-]", "", ref)

        try:
            Rqurl = str(url + str(ref))
            res = requests.get(Rqurl, timeout=timeout)
            if res.status_code == 200:
                trackData = res.json()
                results[ref] = trackData
                # 判断 trackData.code = 200 不然就是失败
                if trackData["code"] != 200:
                    # results[ref] = {"error": "请求失败"}
                    print(trackData, Rqurl)
                    # 抛出try的异常
                    raise Exception("请求失败" + str(trackData["msg"]))

                    return

                # 写入数据
                with save_lock:
                    save_to_sqlite(
                        ref,
                        {
                            "trackData": json.dumps(trackData, ensure_ascii=False),
                            "T1": trackData["data"]["TimeStatistics"]["T1"],
                            "T2": trackData["data"]["TimeStatistics"]["T2"],
                            "T3": trackData["data"]["TimeStatistics"]["T3"],
                            "T4": trackData["data"]["TimeStatistics"]["T4"],
                            "TPT": trackData["data"]["TimeStatistics"]["TPT"],
                            "CarrierName": trackData["data"]["StatusListConfig"][
                                "CarrierName"
                            ],
                            "OriginPort": trackData["data"]["OriginPort"],
                            "DestPort": trackData["data"]["DestPort"],
                        },
                    )

        except Exception as e:
            results[ref] = {"error": str(e)}
            print(f"请求失败 {ref}" + str(e))

    # save_to_sqlite(
    # "SKP4534124792",
    #    { trackData:"",  T1: 1, T2: 2, T3: 3, T4: 4, TPT: 5, CarrierName: "CarrierName", OriginPort: "OriginPort", DestPort: "DestPort"},
    # )
    def save_to_sqlite(ref, kvData):
        c, conn = connect_sqlite()
        # 保存到数据库
        c.execute(
            f"""
            UPDATE shipment SET trackData = ?, T1 = ?, T2 = ?, T3 = ?, T4 = ?, TPT = ?, CarrierName = ?, OriginPort = ?, DestPort = ? WHERE connote = ?
            """,
            (
                kvData["trackData"],
                kvData["T1"],
                kvData["T2"],
                kvData["T3"],
                kvData["T4"],
                kvData["TPT"],
                kvData["CarrierName"],
                kvData["OriginPort"],
                kvData["DestPort"],
                ref,
            ),
        )
        conn.commit()

    # 多线程 请求 显示进度 比如 1/10
    with ThreadPoolExecutor(max_thread) as executor:
        for index, item in enumerate(list, start=1):
            executor.submit(get_track, index, item["ref"])

        # 进度条
        while len(results) < len(list):
            print(f"进度:{len(results)}/{len(list)}", end="\r")
            time.sleep(0.1)

    # 保存到文件
    with open("tracksTemp.json", "w") as f:
        f.write(json.dumps(results, ensure_ascii=False))


# 生成报告 Excle
# 运输方式 单号 始发地 目的地 揽收日期 离港日期 抵港日期 清关日期 入仓日期 入仓总数量 货物重量(KG) 货物体积(CBM) 运单号 船名航次 T1 T2 T3 T4 TPT Mon月份
def generateReport():
    # 其中 揽收日期 离港日期 抵港日期 清关日期 入仓日期 在 trackData.data.StatusList 中 比如
    # 揽收日志 trackData.data.StatusList.CN Plan
    # 离港日期 trackData.data.StatusList.ETD
    # 抵港日期 trackData.data.StatusList.ETA
    # 清关日期 trackData.data.StatusList.Cleared
    # 入仓日期 trackData.data.StatusList.Delivered

    # 读取数据
    c, conn = connect_sqlite()
    data = c.execute(
        """
        SELECT 
            mode, ref, OriginPort, DestPort, indate, 
            trackData, 
            packs, weight, cbm, connote, CarrierName, 
            T1, T2, T3, T4, TPT, country
        FROM shipment
        WHERE trackData IS NOT NULL
        """
    ).fetchall()

    # 生成报告
    report = []
    for item in data:
        trackData = json.loads(item[5])
        report.append(
            {
                "mode": item[0],
                "country": item[16],
                "ref": item[1],
                "OriginPort": item[2],
                "DestPort": item[3],
                "indate": item[4],
                "CN Plan": trackData["data"]["StatusList"]["CN Plan"],
                "ATD": trackData["data"]["StatusList"]["ATD"],
                "ATA": trackData["data"]["StatusList"]["ATA"],
                "Cleared": trackData["data"]["StatusList"]["Cleared"],
                "Delivered": trackData["data"]["StatusList"]["Delivered"],
                "packs": item[6],
                "weight": item[7],
                "cbm": item[8],
                "connote": item[9],
                "CarrierName": item[10],
                "T1": item[11],
                "T2": item[12],
                "T3": item[13],
                "T4": item[14],
                "TPT": item[15],
            }
        )

    # 保存到文件 excel
    with open("report.csv", "w", newline="") as f:
        writer = csv.DictWriter(
            f,
            fieldnames=[
                "mode",
                "country",
                "ref",
                "OriginPort",
                "DestPort",
                "indate",
                "CN Plan",
                "ATD",
                "ATA",
                "Cleared",
                "Delivered",
                "packs",
                "weight",
                "cbm",
                "connote",
                "CarrierName",
                "T1",
                "T2",
                "T3",
                "T4",
                "TPT",
            ],
        )
        writer.writeheader()
        writer.writerows(report)


def delConnote(connoteList, key):
    # 删除 connoteList 是一个列表
    # key 是数据库的字段 需要清空内容为null
    c, conn = connect_sqlite()
    placeholders = ", ".join("?" for _ in connoteList)
    query = f"UPDATE shipment SET {key} = NULL WHERE ref IN ({placeholders})"
    c.execute(query, connoteList)
    conn.commit()


from tla import AUSOAClient


# 获取远端的fbaid
def getFbaData():

    ausoa = AUSOAClient()
    # 读取 connote.txt 一行一个单号
    with open("connote.txt", "r") as f:
        connoteList = f.readlines()
        connoteList = [item.strip() for item in connoteList]
        # print(connoteList)
        # 获取单号的详细信息
        results = ausoa.AUSOAGetShipmentDetail(connoteList)
        # print(results)

        # 遍历 结果 获取 ref

        data = []

        if results.get("code") == 200:
            for index, item in enumerate(results.get("data")):
                # print(item["ref"])
                # 组织数据
                data.append(
                    {
                        "ref": item["ref"],
                        "amazon_shipment_ids": item["amazon_shipment_ids"],
                        "amazon_po": item["amazon_po"],
                        "amazon_unit": item["amazon_unit"],
                    }
                )
        else:
            print(results)

        # 获取单号的轨迹数据
        dbSave(connoteList)
        # 获取本地数据
        # 创建数据

        reqList = []
        # 遍历 connoteList item 就是 ref
        for item in connoteList:
            reqList.append(
                {
                    "ref": item,
                }
            )

        getTracks(
            reqList,
            "http://localhost.ptlogin2.qq.com:6608/scintl-openapi/api/index.php?ver=local&language=zh&debug=tss&OrderId=",
            # "https://api.gz.quannanguoji.com/openapi/?ver=dalton&debug=tss&language=zh&OrderId=",
            5,
        )

        # 输出 csv
        with open("fba.csv", "w", newline="") as f:
            writer = csv.DictWriter(
                f,
                fieldnames=["ref", "amazon_shipment_ids", "amazon_po", "amazon_unit"],
            )
            writer.writeheader()
            writer.writerows(data)


def main():

    # 清空数据
    # 读取目录的del.txt 一行一个单号
    with open("del.txt", "r") as f:
        delList = f.readlines()
        delList = [item.strip() for item in delList]
        # 删除
        delConnote(delList, "trackData")

    getData("tssTest", "http://api.scoa.fbatoll.com/k/httpDB.php", 10)

    # 读取数据 没有trakcData的
    c, conn = connect_sqlite()
    list = c.execute("SELECT connote FROM shipment WHERE trackData IS NULL").fetchall()
    # 组织数据
    list = [{"ref": item[0]} for item in list]

    # print(list)
    getTracks(
        list,
        "http://localhost.ptlogin2.qq.com:6608/scintl-openapi/api/index.php?ver=local&language=zh&debug=tss&OrderId=",
        # "https://api.gz.quannanguoji.com/openapi/?ver=dalton&debug=tss&language=zh&OrderId=",
        10,
    )

    generateReport()


if __name__ == "__main__":
    getFbaData()
    # main()
