import json
import re
import threading
import time
from concurrent.futures import Thread<PERSON>oolExecutor, TimeoutError

import requests


class OAClient:
    def __init__(self):
        pass

    def getData(self, key, url, timeout):
        jsonData = {
            "key": key,
            "sql": """
SELECT 
    e.billid, 
    e.transbillid,
    e.indate,
    e.rchannelid, 
    e.country,
    e.rrweight,
    e.rmcal,
    w.signid,
    e.rmweight,
    e.goodsnum,
    c.clientname,
    CASE 
        WHEN air_items.sheetid IS NOT NULL THEN 'air'
        ELSE 'sea'
    END AS transport_mode
FROM 
    p_exportItem e
LEFT JOIN 
    client c ON e.clientid = c.clientid
LEFT JOIN 
    t_user u ON c.salemanid = u.userid
LEFT JOIN 
    billstatus w ON e.billid = w.billid
LEFT JOIN 
    (SELECT sheetid FROM p_airItem) air_items ON e.sheetid = air_items.sheetid
WHERE 
    e.indate > '2024-10-01' 
    AND e.indate < '2026-01-01'
ORDER BY 
    e.sheetid DESC
;
                    """,
        }

        res = requests.post(url, json=jsonData, timeout=timeout)
        # 保存到本地
        with open("dbData.json", "w") as f:
            f.write(json.dumps(res.json(), ensure_ascii=False))

    def getTracks(self, list, url, timeout):
        # 多线程请求结果

        max_thread = 30
        results = {}
        timeout_refs = []  # 记录超时的单号

        # 请求
        def get_track(index, ref):
            print("请求中" + str(ref))

            # ref 只保留 字母和数字-_符号
            # ref = re.sub(r"[^\w-]", "", ref)

            try:
                RqUrl = str(url + str(ref))
                res = requests.get(RqUrl, timeout=timeout, verify=False)

                # debug
                # print(str(res.status_code) + " " + RqUrl)
                # print(res.text)
                if res.status_code == 200:
                    trackData = res.json()
                    # 判断 trackData.code = 200 不然就是失败
                    if trackData["code"] != 200:
                        print(trackData, RqUrl)
                        # 抛出try的异常
                        raise Exception("请求失败" + str(trackData["msg"]))
                        return

                    # 组织数据
                    results[ref] = {
                        "trackData": json.dumps(trackData, ensure_ascii=False),
                        "T1": trackData["data"]["TimeStatistics"]["T1"],
                        "T2": trackData["data"]["TimeStatistics"]["T2"],
                        "T3": trackData["data"]["TimeStatistics"]["T3"],
                        "T4": trackData["data"]["TimeStatistics"]["T4"],
                        "TPT": trackData["data"]["TimeStatistics"]["TPT"],
                        "CarrierName": trackData["data"]["StatusListConfig"][
                            "CarrierName"
                        ],
                        "OriginPort": trackData["data"]["OriginPort"],
                        "DestPort": trackData["data"]["DestPort"],
                    }
                else:
                    print(
                        f"服务器返回非 200 状态码: {res.status_code} - {ref} - {res.text}"
                    )
                    results[ref] = {"error": f"HTTP 错误: {res.status_code}"}

            except requests.Timeout:
                timeout_refs.append(ref)
                results[ref] = {"error": "请求超时"}
                print(f"请求超时 {ref}")
            except Exception as e:
                results[ref] = {"error": str(e)}
                print(f"请求失败 {ref}" + str(e))

        # 多线程 请求 显示进度 比如 1/10
        with ThreadPoolExecutor(max_thread) as executor:
            futures = []
            for index, ref in enumerate(list, start=1):
                futures.append(executor.submit(get_track, index, ref))

            # 进度条
            start_time = time.time()
            while len(results) < len(list):
                print(f"进度:{len(results)}/{len(list)}", end="\r")
                time.sleep(0.1)

                # 如果整体执行时间超过timeout的2倍，强制退出
                if time.time() - start_time > timeout * 2:
                    print(f"\n整体执行超时，强制退出")
                    # 记录未完成的单号
                    for ref in list:
                        if ref not in results:
                            timeout_refs.append(ref)
                            results[ref] = {"error": "整体执行超时"}
                    break

        if timeout_refs:
            print(f"\n超时单号列表: {timeout_refs}")

        return results


# 测试
# if __name__ == "__main__":
#     oa = OAClient()
#     list = ["SKP4534079081", "SKP4534079649"]
#     url = "https://cdn.gz.shichengguoji.com/openapi/?ver=local&language=zh&debug=tss&OrderId="
#     print(oa.getTracks(list, url, 5))

# 测试 2
# if __name__ == "__main__":
#     oa = OAClient()
#     oa.getData("tssTest", "http://api.scoa.fbatoll.com/k/httpDB.php", 10)
