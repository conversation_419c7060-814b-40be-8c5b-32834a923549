import re


class ConnoteChecker:
    def __init__(self):
        self.preg_arr = [
            {
                "name": "Australia Post",
                "preg": r"^(?=.*\d{2}[a-zA-Z0-9]{1}[a-zA-Z0-9]{1}[a-zA-Z]{1}\d{7}$).{12}$",
                "url": "http://auspost.com.au/parcels-mail/track.html#/track?id={connote}&postcode={postcode}",
            },
            {
                "name": "Australia Post",
                "preg": r"^(?=.*[aA][mM][qQ]\d{7}).{10}$",
                "url": "http://auspost.com.au/parcels-mail/track.html#/track?id={connote}&postcode={postcode}",
            },
            {
                "name": "Truck",
                "preg": r"^(?=.*[a-zA-Z]{1}[Kk][Pp]\d{10}).{13}$",
                "url": "https://logisticsaus.com/Track/?connote={connote}&postcode={postcode}",
            },
            {
                "name": "Truck Parcel",
                "preg": r"^(?=.*[Tt][Ll][Dd]\d{16}).{19}$",
                "url": "https://logisticsaus.com/Track/?connote={connote}&postcode={postcode}",
            },
            {
                "name": "Connote",
                "preg": r"^(?=.*[pP][iI][cC][kK][uU][pP].{8}$).{14}$",
                "url": "https://logisticsaus.com/Track/?connote={connote}&postcode={postcode}",
            },
            {
                "name": "SYS",
                "preg": r"^(?=.*[Tt][a-zA-Z]{1}[Nn]\d{10}).{13}$",
                "url": "https://logisticsaus.com/Track/?connote={connote}&postcode={postcode}",
            },
            {
                "name": "TNT",
                "preg": r"^(?=.*[tT][lL][sS]\d{9}).{12}$",
                "url": "https://logisticsaus.com/Track/?connote={connote}&postcode={postcode}",
            },
            {
                "name": "Allied",
                "preg": r"^(?=.*[tT][pP][lL]\d{9}).{12}$",
                "url": "https://www.alliedexpress.com.au?consignment={connote}&postcode={postcode}",
            },
            {
                "name": "TOLL",
                "preg": r"^(?=.*8\d{1}6\d{4}0\d{5}$).{13}$",
                "url": "https://www.myteamge.com/?externalSearchQuery={connote}&postcode={postcode}",
            },
            {
                "name": "iMile Delivery",
                "preg": r"^(?=397\d{10}$).{13}$",
                "url": "https://www.imile.com/zh/track/?connote={connote}&postcode={postcode}",
            },
            {
                "name": "Aramex",
                "preg": r"^(?=[mM][a-zA-Z]{1}\d{10}$).{12}$",
                "url": "https://www.aramex.com.au/tools/track/?l={connote}&postcode={postcode}",
            },
            {
                "name": "Aramex",
                "preg": r"^(?=2[xX]\d{10}$).{12}$",
                "url": "https://www.aramex.com.au/tools/track/?l={connote}&postcode={postcode}",
            },
            {
                "name": "CouriersPlease",
                "preg": r"^(?=[cC][pP]\S{5}\d{10}$).{17}$",
                "url": "https://www.couriersplease.com.au/tools-track?no={connote}&postcode={postcode}",
            },
            {
                "name": "Hunter",
                "preg": r"^(?=.*[aA][bB][cC]\d{6}$).{9}$",
                "url": "https://www.hunterexpress.com.au/tracking/?connote={connote}&postcode={postcode}",
            },
        ]

    def check_connate(self, connote, arr=False, data={}):
        for item in self.preg_arr:
            pattern = re.compile(item["preg"], re.IGNORECASE)
            if pattern.match(connote):
                # Replace {connote} in URL with actual connote value
                if "url" in item:
                    item["url"] = item["url"].format(
                        connote=connote, postcode=data.get("postcode", "")
                    )

                # Replace other placeholders in the URL with actual data
                for k, v in data.items():
                    json_string = str(item)
                    json_string = json_string.replace(f"{{{k}}}", v)
                    item = eval(
                        json_string
                    )  # Note: Using eval here is not safe for arbitrary strings

                if arr:
                    item.pop("preg", None)  # Remove preg if returning array
                    return item
                return item["name"]

        return False
