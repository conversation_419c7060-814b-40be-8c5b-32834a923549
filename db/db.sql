-- database: ./data.db

SELECT * FROM track WHERE status IS NULL OR status != "Delivered";


-- 要运行整个文件，请按窗口右上角的▷按钮。

SELECT * FROM track;
-- 清空数据表

DELETE FROM track;

-- 重置自增列

DELETE FROM sqlite_sequence WHERE name='track';

-- 收缩数据库 sqlite
VACUUM;

-- 创建
CREATE TABLE "track" (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    connote TEXT UNIQUE,
    ref TEXT,
    cref TEXT,
    data TEXT,
    dataSubmit TEXT,
    dataMore TEXT,
    dataRef TEXT,
    status TEXT,
    type TEXT,
    remark TEXT,
    created datetime,
    modified datetime
    )

    