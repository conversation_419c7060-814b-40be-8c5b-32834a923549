import hashlib
import json
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

import requests

API_URL = "https://api.toplogistics.com.au"
API_ID = "YZGJ"
API_KEY = "bd8d9c3f45278ef7bf908eadb8c9690301ccdb650174874df9b250d3cef793c7"
REQUEST_TIMEOUT = 120  # 请求的超时时间（秒）
MAX_CONNOTE_PER_REQUEST = 100  # 每个请求的最大单号数量
# 最大进程数
MAX_WORKERS = 10


class AUSOAClient:
    def __init__(self):
        self.api_url = API_URL
        self.api_key = API_KEY
        self.api_id = API_ID

    def hash_auth(self, data):
        # 对字典进行升序排列
        sorted_data = sorted(data.items())

        # 拼接数据
        T_str = self.api_key
        for key, value in sorted_data:
            if len(value) > 0:  # 大于0才拼接
                T_str += key
                T_str += value
        T_str += self.api_key

        # 生成验证sign
        T_sign = hashlib.md5(T_str.encode("utf-8")).hexdigest().upper()
        return T_sign

    def _get_shipment_detail_chunk(self, connote_chunk, _type, results, index):
        # 输出进度
        print(f"获取详细进程 {index+1} / {str(len(results))}", end="\r")
        try:
            url = self.api_url + "/shipment/get"
            data = json.dumps({_type: connote_chunk})
            send_data = {
                "api_id": self.api_id,
                "data": data,
                "method": "get",
            }
            send_data["sign"] = self.hash_auth(send_data)
            response = requests.post(url, data=send_data, timeout=REQUEST_TIMEOUT)
            if response.status_code != 200:
                raise Exception("请求失败，状态码异常")
            response_data = response.text
            if response_data == "False" or len(response_data) < 1:
                raise Exception("服务器返回数据为空")
            response_data = json.loads(response_data)
            # print(response_data)
            if response_data["status"] != 1:
                raise Exception("status 不为1")
            results[index] = response_data["shipments"]
        except Exception as e:
            results[index] = {"error": str(e)}

    def AUSOAGetShipmentDetail(self, connoteArr, _type="ref", debug=False):
        results = {
            "code": 500,
            "msg": "",
            "data": [],
        }
        try:
            if not isinstance(connoteArr, list):
                raise ValueError("connoteArr 不是数组")

            results_chunks = [None] * (
                (len(connoteArr) + MAX_CONNOTE_PER_REQUEST - 1)
                // MAX_CONNOTE_PER_REQUEST
            )
            with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
                futures = []
                for i in range(0, len(connoteArr), MAX_CONNOTE_PER_REQUEST):
                    connote_chunk = connoteArr[i : i + MAX_CONNOTE_PER_REQUEST]
                    futures.append(
                        executor.submit(
                            self._get_shipment_detail_chunk,
                            connote_chunk,
                            _type,
                            results_chunks,
                            i // MAX_CONNOTE_PER_REQUEST,
                        )
                    )

                for future in as_completed(futures):
                    future.result()

            for result in results_chunks:
                if isinstance(result, dict) and "error" in result:
                    print("这个请求错误：" + str(result))
                    # raise Exception(result["error"])
                else:
                    results["data"].extend(result)
            results["code"] = 200
        except Exception as e:
            results = {
                "code": 500,
                "msg": str(e),
                "data": [],
            }
        return results

    def _get_tracking_detail_chunk(self, connote_chunk, _type, results, index, debug):
        # 输出进度
        print(f"获取轨迹进程 {index+1} / {str(len(results))}", end="\r")
        try:
            url = self.api_url + "/tracking"
            data = json.dumps({_type: connote_chunk})
            send_data = {
                "api_id": self.api_id,
                "data": data,
            }
            send_data["sign"] = self.hash_auth(send_data)
            response = requests.post(url, data=send_data, timeout=REQUEST_TIMEOUT)
            if response.status_code != 200:
                raise Exception("请求失败，状态码异常")
            response_data = response.text
            if response_data == "False" or len(response_data) < 1:
                raise Exception("服务器返回数据为空")
            response_data = json.loads(response_data)
            # 判断
            if len(response_data) == 0:
                raise Exception("服务器返回数据长度为0")
            else:
                results[index] = response_data
                return
            raise Exception("异常结束")
        except Exception as e:
            results[index] = {"error": str(e)}

    def AUSOATracking(self, connoteArr, _type="ref", debug=False):
        results = {
            "code": 500,
            "msg": "",
            "data": [],
        }
        try:
            if not isinstance(connoteArr, list):
                raise ValueError("connoteArr 不是数组")

            results_chunks = [None] * (
                (len(connoteArr) + MAX_CONNOTE_PER_REQUEST - 1)
                // MAX_CONNOTE_PER_REQUEST
            )
            with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
                futures = []
                for i in range(0, len(connoteArr), MAX_CONNOTE_PER_REQUEST):
                    connote_chunk = connoteArr[i : i + MAX_CONNOTE_PER_REQUEST]
                    futures.append(
                        executor.submit(
                            self._get_tracking_detail_chunk,
                            connote_chunk,
                            _type,
                            results_chunks,
                            i // MAX_CONNOTE_PER_REQUEST,
                            debug,
                        )
                    )

                for future in as_completed(futures):
                    future.result()

            for result in results_chunks:
                if isinstance(result, dict) and "error" in result:
                    # raise Exception(result["error"])
                    print("这个请求错误：" + str(result))
                # print("data:")
                # print(result)
                # results["data"].extend(result)
                # result 是一个字典，key是单号，value是单号的详细信息
                # 有 才处理
                if result == []:
                    continue

                for key, value in result.items():
                    results["data"].append(value)
            # 判断是否有数据
            if len(results["data"]) == 0:
                raise Exception("没有数据")

            results["code"] = 200
        except Exception as e:
            results = {
                "code": 500,
                "msg": str(e),
                "data": [],
            }
        return results


# 测试
# connoteArr = [
#     "TPL000070634",
#     "TPL000071935",
#     "TPL000071935",
# ]
# ausoa = AUSOAClient()
# # 获取单号的详细信息
# # results = ausoa.AUSOAGetShipmentDetail(connoteArr)
# results = ausoa.AUSOATracking(connoteArr)
# print(results)
