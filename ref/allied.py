import datetime
import random
import re

import requests
import urllib3
from bs4 import BeautifulSoup

urllib3.disable_warnings()

timeoutSet = 10


class AlliedTrack:
    def getData(self, order_id, postcode):
        # print("Getting data for connote: ", order_id + " and postcode: ", postcode)

        results = {
            "code": 0,
            "msg": "",
            "data": [],
        }

        try:
            # 获取token
            token = self.getToken()
            # 如果没有token 直接exception
            if token is None:
                raise Exception("Token not found")

            url = "https://secure-neptune.alliedexpress.com.au/iTrack/search.do"
            headers = {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "Cache-Control": "max-age=0",
                "Content-Type": "application/x-www-form-urlencoded",
                "Referer": "https://www.alliedexpress.com.au/",
            }
            data = {
                "sig": token,
                "tno": order_id,
                "ono": "",
                "searchType": "con",
                "doc": order_id,
                "dpc": postcode,
                "searchButton": "Search",
            }
            # 不验证https证书
            response = requests.post(url, headers=headers, data=data, verify=False)
            # 判断是否请求成功
            if response.status_code != 200:
                raise Exception("Request failed")

            tracking_TRACK = []  # 轨迹
            tracking_POD_URL = []  # 签收图片
            tracking_EDATE = ""  # 预计签收时间
            tracking_NEWS = ""  # 最新状态
            tracking_STATUS = ""  # 最新状态码

            html = response.text

            # 用类似于jQuery的方式来解析html
            # 轨迹 在 #scansTranslatorTable .shipmentListSpace 下每个li里面的内容
            # 先匹配 #scansTranslatorTable .shipmentListSpace
            soup = BeautifulSoup(html, "html.parser")
            shipment_list = soup.select("#scansTranslatorTable .shipmentListSpace li")

            # li 里面 h4 是 info 内容 span.fieldDate 是时间 .currentLocation下的<span> 是位置
            for li in shipment_list:
                info = li.select("h4")
                date = li.select("span.fieldDate")
                location = li.select(".currentLocation span")

                if info and date and location:
                    info_text = info[0].get_text(strip=True)
                    date_text = date[0].get_text(strip=True)
                    location_text = location[0].get_text(strip=True)
                    tracking_TRACK.append(
                        {
                            "info": info_text,
                            "date": date_text,
                            "location": location_text,
                        }
                    )

            # 签收图片 在 iframe标签 title=Delivery Photographs
            # 先匹配 iframe 获取 url即可
            PODiframe = soup.select("iframe[seamless='seamless']")
            if PODiframe:
                tracking_POD_URL = PODiframe[0]["src"]

            # 获取预计签收时间
            # 在 id=jobDetailsContainer 第一个 span标签 class=value
            EDATE = soup.select("#jobDetailsContainer td table span.value")
            if EDATE:
                tracking_EDATE = EDATE[0].get_text(strip=True)
                # 转为 2021-01-01 格式
                tracking_EDATE = datetime.datetime.strptime(
                    tracking_EDATE, "%d/%m/%Y"
                ).strftime("%Y-%m-%d")

            # 获取最新状态
            # $pattern_news = '/<h2 style="color:#FF0000">(.*?)<\/h2>/s';
            Status_msg = re.findall(r'<h2 style="color:#FF0000">(.*?)</h2>', html)
            if Status_msg:
                tracking_NEWS = Status_msg[0]

            # 获取 status 判断最后一条轨迹 是否包含 "DELIVERED" 不区分大小写
            last_track = tracking_TRACK[-1]["info"]
            if "DELIVERED" in last_track.upper():
                tracking_STATUS = "DELIVERED"
            else:
                # 没有签收就没有pod
                tracking_POD_URL = ""

            # 组织内容
            results["code"] = 1
            results["data"] = {
                "TRACK": tracking_TRACK,
                "POD_URL": tracking_POD_URL,
                "EDATE": tracking_EDATE,
                "NEWS": tracking_NEWS,
                "STATUS": tracking_STATUS,
                "TYPE": "Allied",
                "REF": order_id,
                "TIMEZEON": "SYD",
            }

            # print("Response: ", response.text)

        except Exception as e:
            # 设置错误信息
            results["msg"] = str(e)

        return results

    def getToken(self):
        # 请求网页获取Token
        url = "https://www.alliedexpress.com.au"
        headers_Arr = [
            "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.95 Safari/537.36",
            "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.71 Safari/537.36",
            "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.65 Safari/537.36",
            "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.37 Safari/537.36",
            "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.99 Safari/537.36",
        ]
        # 随机选择一个header
        headers = {"User-Agent": headers_Arr[random.randint(0, 4)]}
        # 请求网页
        response = requests.get(url, headers=headers, timeout=timeoutSet)
        # 提取 <input name="sig" type="hidden" value="0C5E96954FB23091E0E4A795C8063C67" /> 里面的 value
        # 判断是否请求成功
        if response.status_code == 200:
            # 正则表达式匹配
            token = re.findall(
                r'<input name="sig" type="hidden" value="(.+?)" />', response.text
            )
            # 判断是否匹配成功
            if len(token) > 0:
                return token[0]
            else:
                return None
        else:
            return None


# debug代码 测试单号 TPL000076619 2478
# if __name__ == "__main__":
#     allied = AlliedTrack()
#     print(allied.getData("TPL000076619", "2478"))
