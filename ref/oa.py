# 打单服务器更新


# 'host'     => '**************',
# 'port'     => 3306,
# 'user'     => 'dadan',
# 'password' => 'CnAnGDTC5WymJRha',
# 'charset'  => 'utf8mb4',
# 'database' => 'dadan',

# 链接mysql
import os
import sqlite3
import time

import pymysql

# 定义所有状态 非定义状态就不设置
statusConfig = [
    "Create",
    "CN Packing",
    "ATD",
    "ATA",
    "Held",
    "Cleared",
    "Deconsolidation",
    "Transit",
    "Delivered",
    "RTS",
]


class daDanUpdate:

    def get_conn(self):
        conn = pymysql.connect(
            host="**************",
            port=3306,
            user="dadan",
            password="CnAnGDTC5WymJRha",
            charset="utf8mb4",
            # 设置表
            database="dadan",
        )
        return conn

    def setTrackStatus(self, data):
        # 组织sql 设置状态 设置 表 api_order. statusTrack = status where ref = ref
        conn = self.get_conn()
        cursor = conn.cursor()
        batch_size = 100
        try:
            for i in range(0, len(data), batch_size):
                batch = data[i : i + batch_size]
                refs = [item["ref"] for item in batch]
                statuses = [
                    item["status"] if item["status"] in statusConfig else "Create"
                    for item in batch
                ]
                statuses = [
                    "Create" if status == "New" else status for status in statuses
                ]

                sql = "UPDATE api_order SET statusTrack = CASE ref "
                for ref, status in zip(refs, statuses):
                    sql += f"WHEN '{ref}' THEN '{status}' "
                sql += "END WHERE ref IN (" + ",".join(f"'{ref}'" for ref in refs) + ")"

                cursor.execute(sql)
                conn.commit()
        except Exception as e:
            conn.rollback()
            print(f"Error: {e}")
        finally:
            cursor.close()
            conn.close()
        return True


# if __name__ == "__main__":
#     # r = test()
#     # print(r)

#     dadan = daDanUpdate()

#     data = [
#         {
#             "ref": "SKP4534127871",
#             "status": "Held",
#         }
#     ]
#     r = dadan.setTrackStatus(data)
#     print(r)
