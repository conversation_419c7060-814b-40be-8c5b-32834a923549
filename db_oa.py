# oa 数据获取
import json
import shutil
import sqlite3
import time

import requests

from func import ConnoteChecker


class DBOA:
    def __init__(self, config):
        self.config = config

    def connect_sqlite(self):
        conn = sqlite3.connect("db/data.db")
        c = conn.cursor()
        return c, conn

    def getConnoteList(self, start_date, end_date):
        print(f"+开始获取{start_date}到{end_date}的数据列表~")
        getStatus = 0
        # 这里是因为防止请求失败 需要重复请求两次
        while getStatus <= 3:
            try:
                # 访问一次加一次
                getStatus += 1
                # 获取请求的列表 post
                jsonData = {
                    "key": self.config["key"],
                    # debug：SELECT TOP 1 PERCENT
                    "sql": f"""
SELECT 
    e.billid, 
    e.transbillid,
    e.clientid, 
    e.rchannelid, 
    w.rnote, 
    w.signid,
    c.clientname, 
    c.salemanid, 
    u.username
FROM 
    p_exportItem e
LEFT JOIN 
    client c ON e.clientid = c.clientid
LEFT JOIN 
    t_user u ON c.salemanid = u.userid
LEFT JOIN 
    billstatus w ON e.billid = w.billid
WHERE 
    e.indate >= '{start_date}' 
    AND e.indate < '{end_date}'
    AND e.sheetid IN (
        SELECT sheetid
        FROM (
            SELECT sheetid, info
            FROM p_shipItem
            UNION ALL
            SELECT sheetid, info
            FROM p_airItem
        ) AS combined_results
    )
ORDER BY 
    e.sheetid DESC
;
                    """,
                }
                res = requests.post(
                    self.config["getDataListUrl"],
                    json=jsonData,
                    timeout=self.config["getDataListUrlTimeOut"],
                )

                # 保存结果到temp
                with open("temp/temp.json", "w") as f:
                    f.write(res.text)

                if res.status_code != 200:
                    # 直接报错 抛出 异常 让except处理
                    raise Exception("获取请求的列表失败")
                if int(res.json()["code"]) == 200:
                    print(f"-获取运单的列表成功~")
                    # 获取请求的列表成功
                    data = res.json()["data"]
                    if len(data) <= 0:
                        raise Exception("没有获取到数据")
                    connoteArr = []
                    connoteArrDel = []
                    # 返回数据前处理
                    for key, item in enumerate(data, start=1):
                        # 不刷新的 进度条
                        print(f"进度:{key}/{len(data)}", end="\r")

                        # 处理送达 判断 item["rnote"](字符串) 是否包含  '[送达' 如果不包含则不添加 item["rnote"] 可能等于 =[送达=2024/10/10]
                        # 判断 item["rnote"] 是否存在 如果不存在就设置为空 item["signid"] 也是
                        if not item["rnote"]:
                            item["rnote"] = ""
                        if not item["signid"]:
                            item["signid"] = ""
                        else:
                            item["signid"] = str(item["signid"])

                        if "[送达" in item["rnote"]:
                            connoteArrDel.append(item["billid"])
                            # 跳过本次循环
                            continue

                        skipSignid = [
                            "200",  # 已签收
                            "102",  # 异常件 客服接管
                            "105",  # 已处理 客服接管
                        ]

                        if item["signid"] in skipSignid:
                            # print(f"进度:{key}/{len(data)} 运单号:{item['billid']} 备注:{item['rnote']} 标识:{item['signid']}")
                            # 加入到connoteArrDel
                            connoteArrDel.append(item["billid"])
                            # 跳过本次循环
                            continue

                        if item["billid"] not in connoteArr:
                            # 处理数据
                            # 判断客户 不添加的客户
                            config_stopCompany = self.config["stopCompany"]
                            addConnote = all(
                                x.lower() not in item["clientid"].lower()
                                for x in config_stopCompany
                            )
                            if addConnote == False:
                                connoteArrDel.append(item["billid"])
                                continue

                        #  item["transbillid"] 去除前后空格 异常字符 只能是 Aa-Zz 0-9 - _ 其他字符替换为空
                        item["transbillid"] = item["transbillid"].strip()
                        item["transbillid"] = "".join(
                            [
                                x
                                for x in item["transbillid"]
                                if x.isalnum() or x in ["-", "_"]
                            ]
                        )

                        connoteArr.append(
                            {
                                "connote": item["billid"],
                                "ref": item["transbillid"],
                                "cref": "",
                                "dataMore": {
                                    "clientid": item["clientid"],
                                    "rchannelid": item["rchannelid"],
                                    "clientname": item["clientname"],
                                    "salemanid": item["salemanid"],
                                    "username": item["username"],
                                    "rnote": item["rnote"],
                                },
                            }
                        )

                    print(f"-获取运单的列表成功~ 共{len(connoteArr)}个订单")
                    # 删除数据
                    if len(connoteArrDel) > 0:
                        print(
                            f"-获取运单的列表成功~ 需要删除忽略{len(connoteArrDel)}个订单"
                        )
                        # 删除的单号 写入 list_del.txt
                        with open("temp/list_del_忽略.txt", "a") as f:
                            f.write("\n".join(connoteArrDel))

                        # 这里需要删除不要的数据 可能会不存在
                        c, conn = self.connect_sqlite()
                        # 批量删除数据
                        sql = "DELETE FROM track WHERE connote = ?"
                        c.executemany(sql, [(item,) for item in connoteArrDel])
                        conn.commit()
                        print(f"-删除忽略{len(connoteArrDel)}个订单")

                    # connoteArr 写入 list_insert.txt 覆盖
                    # 清空

                    with open("temp/list_insert_插入.txt", "a") as f:
                        f.write(
                            "\n".join(
                                f"{item['connote']} / {item['ref']} / {item['cref']}"
                                for item in connoteArr
                            )
                        )

                    return connoteArr
                raise Exception("获取请求的列表失败2")
            except Exception as e:
                print(f"错误{str(e)}")
                # 错误了就等5秒再重试
                print(f"等待5秒再重试~{getStatus}")
                time.sleep(5)
        return []

    def setSignid(self, signidArr, signCode, note=""):
        # 这里是因为防止请求失败 需要重复请求两次
        getStatus = 0
        while getStatus <= 3:
            try:
                # 访问一次加一次
                getStatus += 1
                # 获取请求的列表 post
                jsonData = {
                    "key": self.config["key"],
                    "sql": f"""UPDATE billstatus SET signid = {signCode} WHERE billid IN ({",".join(f"'{billid}'" for billid in signidArr)});""",
                }

                # 输出sql 到 当前目录的temp.sql 文件不存在就创建
                # with open("temp.sql", "w") as f:
                #     f.write(jsonData["sql"])
                # print(jsonData)
                # raise Exception("debug")
                res = requests.post(
                    self.config["getDataListUrl"],
                    json=jsonData,
                    timeout=self.config["getDataListUrlTimeOut"],
                )
                if res.status_code != 200:
                    # 直接报错 抛出 异常 让except处理
                    raise Exception("获取请求的列表失败")
                if int(res.json()["code"]) == 200:
                    # print(f"-设置标识成功~")
                    return True
                # print(f"-设置标识失败")
                raise Exception("设置标识失败")
            except Exception as e:
                print(f"错误{str(e)}")
                # 错误了就等5秒再重试
                print(f"等待5秒再重试~{getStatus}")
                time.sleep(5)
        return False

    # 处理超级多数据 分批
    def setSignidMax(self, signidArr, signCode, note="", 每次处理数量=1000):
        次数 = len(signidArr) // 每次处理数量
        if len(signidArr) % 每次处理数量 > 0:
            次数 += 1
        for i in range(次数):
            # 获取数据
            start = i * 每次处理数量
            end = (i + 1) * 每次处理数量
            if end > len(signidArr):
                end = len(signidArr)
            signidArr2 = signidArr[start:end]
            result1 = self.setSignid(signidArr2, signCode)
            print(
                f"状态: {result1} 处理量: {len(signidArr2)} 次数:{i+1}/{次数-1}",
                end="\r",
            )

    def run(self):
        # 启动
        print("开始运行~")

        # 清空原有数据
        c, conn = self.connect_sqlite()
        # 删除数据
        print("-删除原有数据")
        c.execute("DELETE FROM track")
        c.execute("DELETE FROM sqlite_sequence WHERE name='track'")
        conn.commit()
        c.execute("VACUUM;")

        # 计算时间段
        start_base = "2024-01-01"  # 固定起始时间为2024年1月
        current_time = time.strftime("%Y-%m-%d")  # 获取当前时间
        
        # 计算时间段
        time_periods = []
        current_start = start_base
        
        while current_start < current_time:
            # 计算结束时间（当前开始时间加6个月）
            current_start_dt = time.strptime(current_start, "%Y-%m-%d")
            year = current_start_dt.tm_year
            month = current_start_dt.tm_mon + 6
            
            # 处理月份超过12的情况
            if month > 12:
                year += 1
                month -= 12
            
            # 格式化结束时间
            end_date = f"{year}-{month:02d}-01"
            
            # 如果结束时间超过当前时间，使用当前时间
            if end_date > current_time:
                end_date = current_time
            
            time_periods.append((current_start, end_date))
            
            # 更新下一个开始时间
            current_start = end_date

        print("时间段划分如下：")
        for start, end in time_periods:
            print(f"{start} 到 {end}")

        all_connote_arr = []
        
        # 按时间段获取数据
        for start_date, end_date in time_periods:
            print(f"\n处理时间段: {start_date} 到 {end_date}")
            connote_arr = self.getConnoteList(start_date, end_date)
            if connote_arr:
                all_connote_arr.extend(connote_arr)
                print(f"该时间段获取到 {len(connote_arr)} 条数据")
            time.sleep(2)  # 添加短暂延迟，避免请求过于频繁

        print(f"\n总共获取到 {len(all_connote_arr)} 条数据")

        # 写入前检查是否含派送单号
        checker = ConnoteChecker()

        # 将 checker 和 all_connote_arr 对比 把要删除的列出来
        delArr = []
        for item in all_connote_arr:
            if not checker.check_connate(item["ref"]):
                delArr.append(item)

        # 判断ref和connote是否调换 调换的要加回去到all_connote_arr
        addArr = []
        addArr = [item for item in delArr if checker.check_connate(item["connote"])]
        addArrList = []

        # 遍历判断 carrier_name = checker.check_connate(connote)
        # 如果没有 carrier_name 那么删除
        all_connote_arr = [item for item in all_connote_arr if checker.check_connate(item["ref"])]

        # 遍历加入回去 设置
        for item in addArr:
            temp = item.copy()
            temp["cref"] = temp["ref"]
            temp["ref"] = temp["connote"]
            addArrList.append(temp)
            # 根据 connote 判断 all_connote_arr是否已经有了 如果没有就插入temp
            if temp["connote"] not in [item["connote"] for item in all_connote_arr]:
                all_connote_arr.append(temp)

        # 需要调换的单号
        with open("temp/list_del_ref_调换.txt", "a") as f:
            f.write("\n".join(f"{item['connote']} / {item['ref']} / {item['cref']}" for item in addArrList))

        # 删除的单号 写入 temp/list_del_ref.txt
        with open("temp/list_del_ref_删除.txt", "a") as f:
            f.write("\n".join(f"{item['connote']} / {item['ref']}" for item in delArr))

        # 写入数据库
        sql = "INSERT INTO track (connote, ref, cref, dataMore, created) VALUES (?, ?, ?, ?, ?)"
        created = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        
        # 批量写入
        c.executemany(
            sql,
            [
                (
                    item["connote"],
                    item["ref"],
                    item["cref"],
                    json.dumps(item["dataMore"]),
                    created,
                )
                for item in all_connote_arr
            ],
        )
        conn.commit()


if __name__ == "__main__":
    config = {
        "key": "tssTest",
        "getDataListUrl": "http://api.scoa.fbatoll.com/k/httpDB.php",
        "getDataListUrlTimeOut": 10,
        "stopCompany": ["test", "4px"],
    }
    # 删除temp 目录下 所有文件 目录不存在就创建
    shutil.rmtree("temp", ignore_errors=True)
    # 创建目录
    shutil.os.makedirs("temp")

    db_oa = DBOA(config)
    db_oa.run()


# 调用案例
# from db_oa import DBOA

# # 配置参数
# config = {
#     "key": "tssTest",
#     "getDataListUrl": "http://api.scoa.fbatoll.com/k/httpDB.php",
#     "getDataListUrlTimeOut": 10,
#     "stopCompany": [
#         "test",
#         "4px"
#     ],
# }

# # 创建 DBOA 类的实例
# db_oa = DBOA(config)

# # 调用 setSignid 方法
# signidArr = ["GH20240828", "GH20240829"]
# signCode = "200"
# note = "签收"

# # 调用 setSignid 方法并传递参数
# result = db_oa.setSignid(signidArr, signCode, note)

# # 打印结果
# print(f"设置标识结果: {result}")
