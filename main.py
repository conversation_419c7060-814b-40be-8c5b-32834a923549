import csv
import hashlib
import inspect
import json
import os
import re
import sqlite3
import threading
import time
from datetime import datetime, timedelta

import requests

# from db_mysql import MySQLDatabase
# 引入根目录的 db.mysql.py
from db_mysql import MySQLDatabase
from db_oa import DBOA
from func import <PERSON><PERSON><PERSON><PERSON><PERSON>
from ref.allied import AlliedTrack
from ref.oa import daDanUpdate
from ref.tla import AUSOAClient

# 全局参数

THREAD_COUNT = 30  # 控制同时运行的线程数
OUTPUT_CSV = "tracking_results.csv"  # 输出CSV文件名
REQUEST_TIMEOUT = 10  # 请求的超时时间（秒）
ERROR_FILE = "temp/list_error_远端.txt"  # 请求失败的单号文件


def connect_sqlite():
    conn = sqlite3.connect("db/data.db")
    c = conn.cursor()
    return c, conn


def prints(message):
    # 获取调用者的信息
    frame = inspect.currentframe().f_back
    filename = os.path.basename(__file__)
    lineno = frame.f_lineno
    funcname = frame.f_code.co_name

    # 获取当前时间
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # 判断 message 是否是字符串 或 dict 或 list 或 数字 或 其他
    if isinstance(message, str):
        pass
    elif isinstance(message, dict):
        message = json.dumps(message, ensure_ascii=False)
    elif isinstance(message, list):
        message = json.dumps(message, ensure_ascii=False)
    else:
        message = str(message)

    # 格式化日志信息
    log_message = f"{current_time} [{filename}:{lineno}-{funcname}] {message}"

    # 输出到控制台
    print(log_message)

    # 写入日志文件
    with open("logfile.log", "a", encoding="utf-8") as logfile:
        logfile.write(log_message + "\n")


# 锁对象，用于同步线程
lock = threading.Lock()
completed_threads = 0


# 推送数据
def pushData(db_oa):
    # 获取送达的数据
    # status = Delivered 这些是要设置状态 = 200
    c, conn = connect_sqlite()
    # 获取数据
    c.execute("SELECT connote FROM track WHERE status = 'Delivered'")
    dataList = c.fetchall()
    # 设置状态
    signidArr = []
    for item in dataList:
        signidArr.append(item[0])
    signCode = "200"

    # 分批查询 量大会导致sql无法处理超时 每次100个
    prints("送达状态数量:" + str(len(signidArr)))
    db_oa.setSignidMax(signidArr, signCode)

    # 转为json 加入temp缓存目录 尾部添加
    with open("temp/Delivered.json", "w", encoding="utf-8") as f:
        f.write(json.dumps(dataList, ensure_ascii=False))

    # 获取派送中数据 Courier 设置
    c, conn = connect_sqlite()
    # 获取数据
    c.execute("SELECT connote FROM track WHERE status = 'Courier'")
    dataList = c.fetchall()
    # 设置状态
    signidArr = []
    for item in dataList:
        signidArr.append(item[0])
    signCode = "103"
    prints("派送中数量:" + str(len(signidArr)))
    db_oa.setSignidMax(signidArr, signCode)

    # 加入
    with open("temp/Courier.json", "w", encoding="utf-8") as f:
        f.write(json.dumps(dataList, ensure_ascii=False))

    # 获取异常数据 拆柜后5天无轨迹
    c, conn = connect_sqlite()
    # 获取数据
    c.execute(
        "SELECT connote FROM track WHERE remark = '拆柜后5天无轨迹' AND status != 'Delivered'"
    )
    dataList = c.fetchall()
    # 设置状态
    signidArr = []
    for item in dataList:
        signidArr.append(item[0])
    signCode = "301"
    prints("拆柜后5天无轨迹数量:" + str(len(signidArr)))
    db_oa.setSignidMax(signidArr, signCode)

    # 加入
    with open("temp/拆柜后5天无轨迹.json", "w", encoding="utf-8") as f:
        f.write(json.dumps(dataList, ensure_ascii=False))

    # 获取异常数据 Border开箱查验36个小时不Cleared
    c, conn = connect_sqlite()
    c.execute(
        "SELECT connote FROM track WHERE remark = 'Border开箱查验36个小时不Cleared' AND status != 'Delivered'"
    )
    dataList = c.fetchall()
    # 设置状态
    signidArr = []
    for item in dataList:
        signidArr.append(item[0])
    signCode = "104"
    prints("Border开箱查验36个小时不Cleared数量:" + str(len(signidArr)))
    db_oa.setSignidMax(signidArr, signCode)

    # 加入日志
    with open("temp/Border开箱查验36个小时不Cleared.json", "w", encoding="utf-8") as f:
        f.write(json.dumps(dataList, ensure_ascii=False))

    # 获取 转单号可能异常
    c, conn = connect_sqlite()
    c.execute(
        "SELECT connote FROM track WHERE remark = '转单号可能异常' AND status != 'Delivered'"
    )
    dataList = c.fetchall()
    # 设置状态
    signidArr = []
    for item in dataList:
        signidArr.append(item[0])
    signCode = "302"
    prints("转单号可能异常数量:" + str(len(signidArr)))
    db_oa.setSignidMax(signidArr, signCode)

    # 加入日志
    with open("temp/转单号可能异常.json", "w", encoding="utf-8") as f:
        f.write(json.dumps(dataList, ensure_ascii=False))


def log_error(order_id):
    """将请求失败的单号写入错误文件"""
    with lock:
        with open(ERROR_FILE, "a") as file:
            file.write(order_id + "\n")


def main_getAUdata():

    # 读取单号列表
    # with open(ORDER_FILE, 'r') as file:
    #     order_ids = [line.strip() for line in file.readlines()]
    # sqlite 读取数据
    c, conn = connect_sqlite()
    order_idsArr = []
    # 读取数据
    c.execute(
        "SELECT connote,ref,dataMore FROM track where status != 'Delivered' OR status IS NULL"
    )
    order_idsArr = c.fetchall()
    # order_ids = [item[0] for item in order_idsArr]

    prints(f"共有{len(order_idsArr)}个订单需要查询")

    ordersData = {}

    # 把所有ref提出来
    refArr = []
    for item in order_idsArr:
        # 没有ref 就跳过
        if item[1] == "":
            continue

        # item[1] 去除前后空格
        # prints("'" + item[1] + "'")
        ref = item[1].lstrip().rstrip()

        ordersData[ref] = {
            "ref": ref,
            "dataSubmit": {},
            "data": {},
            "dataRef": {},
            "status": "",
            "remark": "",
        }
        refArr.append(ref)

    prints("获取运单中...")
    # 获取尾端数据
    ausoa = AUSOAClient()
    refData = ausoa.AUSOAGetShipmentDetail(refArr)

    # 遍历放入 key=ref value=data
    if refData.get("code") == 200:
        prints("获取尾端数据成功 共：" + str(len(refData.get("data"))))
        # prints(refData.get("data"))
        for index, item in enumerate(refData.get("data")):
            try:
                # ref 也要去除前后空格
                ref = item["ref"].lstrip().rstrip()
                # 保存的内容也要去除前后空格
                item["ref"] = ref
                # prints(item["ref"])、
                # 还要判断 ordersData[ref] 是否存在
                if ref in ordersData:
                    ordersData[ref]["dataSubmit"] = item
            # 捕获所有错误
            except Exception as e:
                prints("获取尾端数据异常：" + str(e) + " " + str(item))
            # 所有都执行
            finally:
                # 显示进度
                print(f"进度:{index}/{len(refData.get('data'))}", end="\r")
    else:
        prints("获取尾端数据失败" + refData.get("msg"))

    #  在这里就要遍历数据 判断是否有ordersData[ref]["dataSubmit"]
    for ref, orderData in ordersData.items():
        if not orderData["dataSubmit"] and not orderData["data"]:
            # 转单号数据可能异常 标记 remark = 无效单号
            ordersData[ref]["remark"] = "转单号可能异常"

    prints("获取轨迹中...")
    # 获取data
    refData = ausoa.AUSOATracking(refArr)
    # 遍历放入 key=ref value=data

    prints("获取轨迹成功 共：" + str(len(refData.get("data"))))

    # 把结果转成json 放到 temp/AUSOATracking.json
    with open("temp/AUSOATracking.json", "w", encoding="utf-8") as f:
        f.write(json.dumps(refData, ensure_ascii=False))

    # prints(refData)
    if refData.get("code") == 200:
        prints("获取轨迹 共：" + str(len(refData.get("data"))))
        for index, item in enumerate(refData.get("data")):
            try:
                # 有ref才处理
                if "ref" not in item:
                    prints("没有ref" + str(item))
                    continue
                # prints(item["ref"])
                ref = item["ref"].lstrip().rstrip()
                # 保存的内容也要去除前后空格
                item["ref"] = ref
                # 还要判断 ordersData[ref] 是否存在
                if ref in ordersData:
                    ordersData[item["ref"]]["data"] = item
            except Exception as e:
                prints(f"处理单号 时出错: {index} {e} 数据：{item}")
    else:
        prints("获取轨迹失败" + refData.get("msg"))

    # 整理后端数据了
    ordersDataR = {}
    ordersDataGet = {}
    # 遍历
    for ref, orderData in ordersData.items():
        # 获取派送渠道
        typeTemp = ConnoteChecker().check_connate(ref)

        # 没有ref 直接跳过
        if not ref:
            continue

        type = ""
        data = orderData.get("data", {})
        tracks = data.get("tracks", [])

        for item in tracks:
            # 判断是否包含 Hand Over
            if any(
                keyword in item[3]
                for keyword in ["Hand Over", "Deconsolidation", "Ascan", "Arrived"]
            ):
                # 交给快递公司
                type = typeTemp
                break

        # 判断是否有 type  如果有 加入 ordersDataGet
        if type:
            orderData["type"] = type
            # 目前只支持allied
            if type == "Allied":
                ordersDataGet[ref] = orderData
                continue

        # 接下来开始处理
        # 加入 ordersDataR
        ordersDataR[ref] = orderData

    # 判断 ordersDataGet 是否有值
    if ordersDataGet and len(ordersDataGet) > 0:
        prints("获取快递数据中...共" + str(len(ordersDataGet)))
        # 创建线程池 这个线程池是去处理还需要获取单独数据的
        threads = []
        total_orders = len(ordersDataGet.items())
        for index, (ref, orderData) in enumerate(ordersDataGet.items()):
            while True:
                if len(threads) < THREAD_COUNT:
                    thread = threading.Thread(
                        target=fetch_data,
                        args=(orderData, index, total_orders, ordersDataGet),
                    )
                    threads.append(thread)
                    thread.start()
                    break
                else:
                    # 清理已完成的线程
                    threads = [t for t in threads if t.is_alive()]

        # 等待所有线程完成
        for thread in threads:
            thread.join()

    # 合并结果
    ordersData = {}
    ordersData.update(ordersDataR)
    ordersData.update(ordersDataGet)

    prints("数据整理中...")
    # 整理数据
    # ordersData
    for ref, orderData in ordersData.items():
        # 判断 data的 status
        if orderData["data"] and "status" in orderData["data"]:
            orderData["status"] = orderData["data"]["status"]

        # 判断是否有 dataRef 内容 如果有 就检查stats是否是Delivered
        if (
            orderData["dataRef"] != {}
            and "STATUS" in orderData["dataRef"]
            and orderData["dataRef"]["STATUS"].lower() == "delivered"
        ):
            # 设置状态
            orderData["status"] = "Delivered"

        # 检查轨迹
        # 组织一个临时轨迹
        tempTracks = []
        if orderData["data"]:
            if "tracks" in orderData["data"]:
                tempTracks = orderData["data"]["tracks"]

        # 判断是否有 dataRef 内容 如果有 加入到轨迹中
        if orderData["dataRef"] != {}:
            if "TRACK" in orderData["dataRef"]:
                # 遍历加入 tempTracks
                for item in orderData["dataRef"]["TRACK"]:
                    tempTracks.append(
                        [item["info"], item["location"], item["date"], ""]
                    )

        # 判断是否有tempTracks
        if tempTracks != []:
            try:
                checkTracks_r = checkTracks(tempTracks)
            except Exception as e:
                prints(f"检查轨迹时出错: {e} 单号：{ref}")
            # prints(checkTracks_r)
            # 返回的是无序的数组 判断是否有值 有值就设置remark 默认[0]
            if checkTracks_r:
                # 这个时候要判断 是否还有remark 如果有就不要覆盖
                if orderData["remark"] == "":
                    orderData["remark"] = checkTracks_r[0]
        # prints(orderData)

    # prints(ordersData)
    prints("数据写入中...")
    # 写入数据库
    try:
        c, conn = connect_sqlite()
        update_data = [
            (
                orderData["status"],
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                (
                    json.dumps(orderData["data"], ensure_ascii=False)
                    if orderData["data"]
                    else ""
                ),
                (
                    json.dumps(orderData["dataSubmit"], ensure_ascii=False)
                    if orderData["dataSubmit"]
                    else ""
                ),
                (
                    json.dumps(orderData["dataRef"], ensure_ascii=False)
                    if orderData["dataRef"]
                    else ""
                ),
                orderData["remark"],
                ref,
            )
            for ref, orderData in ordersData.items()
            if orderData["status"]
            or orderData["remark"]
            or orderData["data"]
            or orderData["dataSubmit"]
            or orderData["dataRef"]
        ]

        # Split update_data into smaller batches
        batch_size = 100  # Adjust the batch size as needed
        for i in range(0, len(update_data), batch_size):
            batch = update_data[i : i + batch_size]
            c.executemany(
                """
                UPDATE 
                    track 
                SET 
                    status = ?, 
                    modified = ?,
                    data = ?, 
                    dataSubmit = ?, 
                    dataRef = ?,
                    remark = ? 
                WHERE
                    ref = ?
                """,
                batch,
            )
            conn.commit()
    except sqlite3.Error as e:
        prints(f"数据库写入失败: {e}")
    finally:
        conn.close()
        prints("数据写入完成")

    return ordersData


def fetch_data(orderData, index, total, ordersDataGet):
    global completed_threads
    # 处理单个的数据
    try:

        responseREF = {
            "code": 0,
            "data": {},
        }

        # 如果是 AlliedTrack
        if orderData["type"] == "Allied":
            # 有 orderData["dataSubmit"]["consignee"]["postcode"] 才能获取 其中 ["consignee"]["postcode"]可能为空
            # prints(orderData)
            if (
                orderData["dataSubmit"]
                and "consignee" in orderData["dataSubmit"]
                and "postcode" in orderData["dataSubmit"]["consignee"]
            ):
                responseREF = AlliedTrack().getData(
                    orderData["ref"], orderData["dataSubmit"]["consignee"]["postcode"]
                )

        # 更多的快递公司
        # 判断是否成功
        if responseREF["code"] == 1:
            # prints("获取成功：" + responseREF["data"]["TYPE"])
            ordersDataGet[orderData["ref"]]["dataRef"] = responseREF["data"]

        # prints(ordersData[orderData["ref"]])
    except requests.RequestException as e:
        prints(f"请求失败: {e}")
        log_error(orderData["ref"])  # 记录失败的单号
    finally:
        # 打印当前进度
        with lock:
            completed_threads += 1
            print(
                f"{completed_threads}/{total} —— {index} : {orderData['ref']}", end="\r"
            )


def setCache(db, ordersData):
    # 批量 set_cache_list
    # 生成数据
    set_cache_data = {}

    for ref, orderData in ordersData.items():
        # 默认缓存时间 24 小时 = 1440 分钟
        expiration_time = (datetime.now() + timedelta(minutes=1440)).strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        if orderData["status"] == "Delivered":
            prints("设置状态为Delivered 缓存9999 " + ref)
            expiration_time = "9999-12-31 23:59:59"
        # 判断是否有 orderData["data"]
        if orderData["data"]:
            set_cache_data["AUS_" + ref] = [
                json.dumps(orderData["data"], ensure_ascii=False),
                expiration_time,
            ]
        if orderData["dataRef"] and "TYPE" in orderData["dataRef"]:
            set_cache_data[orderData["dataRef"]["TYPE"] + "_" + ref] = [
                json.dumps(orderData["dataRef"], ensure_ascii=False),
                expiration_time,
            ]

    prints("设置缓存")
    # prints(set_cache_data)
    db.set_cache_list("cache", set_cache_data)


# r = checkTracks(
#         [
#             ["Shipment info received", "", "30/10/2024 21:10:54", "Create"],
#             ["Shipment info confirmed", "", "03/11/2024 14:03:07", "Manifested"],
#             ["Reported To Customs", "SYDNEY", "03/11/2024 16:21:24", ""],
#             ["Flight dispatched", "Hong Kong, HK", "03/11/2024 19:33:00", "Enroute"],
#             ["Customs held(Border)", "SYDNEY", "04/11/2024 04:50:05", "Held"],
#             ["Flight arrived", "Sydney, NSW, AU", "04/11/2024 07:31:00", "Enroute"],
#             ["Arrived at warehouse", "", "04/11/2024 15:16:47", "Arrived"],
#             [
#                 "Deconsolidation has been done",
#                 "",
#                 "04/10/2024 15:40:22",
#                 "Deconsolidation",
#             ],
#         ]
#     )
#     prints(r)
def checkTracks(tracks):

    # 遍历时间统一 处理成 Y-m-d H:i:s
    for index, value in enumerate(tracks):
        try:
            # 尝试匹配 dd/mm/yyyy HH:MM:SS 格式
            if re.match(r"^\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2}$", value[2]):
                parsed_time = datetime.strptime(value[2], "%d/%m/%Y %H:%M:%S")
            # 尝试匹配 yyyy-mm-dd HH:MM:SS 格式
            elif re.match(r"^\d{4}-\d{1,2}-\d{1,2} \d{1,2}:\d{1,2}:\d{1,2}$", value[2]):
                parsed_time = datetime.strptime(value[2], "%Y-%m-%d %H:%M:%S")

            else:
                raise Exception(f"时间格式不匹配: {value[2]}")

            # 直接转为所需格式的字符串
            tracks[index][2] = parsed_time.strftime("%Y-%m-%d %H:%M:%S")
        except Exception as e:
            print(f"时间转换错误：{e}")

    # prints(tracks)
    # 第一个检测是最后的轨迹距今超过5天
    # 第二个检测是 装填 Deconsolidation 后 五天无动作
    # 第三个检测是 Held 状态 并且内容包含Border
    # 开始
    lineArr = []

    # 获取
    if_HeldBorder = False
    if_HeldArr = []
    if_Deconsolidation = []
    if_Cleared = False
    if_lastTrack = []
    if_Delivered = False

    # 获取最后一条轨迹
    lastTrack = tracks[-1]
    # 处理时间
    lastTrackTime = lastTrack[2]

    # 遍历轨迹 tracks
    for value in tracks:
        info = value[0]
        point = value[1]
        time = value[2]
        status = value[3]
        # 处理时间
        time = time
        # 获取
        if status == "Held":
            if_HeldArr = value
            # 检测 info 是否包含 Border 不分大小写
            if "border" in info.lower():
                if_HeldBorder = True
        if status == "Deconsolidation":
            if_Deconsolidation = value
        if status == "Cleared":
            if_Cleared = True
        if status == "Delivered":
            if_Delivered = True

    # 第一个检测是最后的轨迹距今超过5天
    today = datetime.now().strftime("%Y-%m-%d")

    # lastTrackTime 转换为 Y-m-d
    # 如果是 2024-11-21 Y-m-d 就不用转换
    if re.match(r"^\d{4}-\d{2}-\d{2}$", lastTrackTime):
        lastTrackDay = lastTrackTime
    else:
        lastTrackDay = datetime.fromtimestamp(
            datetime.strptime(lastTrackTime, "%Y-%m-%d %H:%M:%S").timestamp()
        ).strftime("%Y-%m-%d")
    today = datetime.strptime(today, "%Y-%m-%d")
    # 计算时间差
    lastTrackDay = datetime.strptime(lastTrackDay, "%Y-%m-%d")

    # 第二个 Deconsolidation 后 五天无动作 拆柜后5天无轨迹，尽快处理！
    # 直接检测最后一条轨迹的状态是否是 Deconsolidation 并且时间超过5天 就可以了
    if if_Deconsolidation:
        # 计算时间差
        diff = today - lastTrackDay
        # 获取时间差
        diff = diff.days
        # 判断是否超过5天
        if diff > 5 and if_Delivered == False:
            lineArr.append("拆柜后5天无轨迹")

    # 第三个检测是 Border开箱查验36个小时不Cleared就通知
    if if_HeldBorder:
        if if_Cleared == False:
            # 判断时间差
            heldDate = if_HeldArr[2]
            # heldDate=转换成Y-m-d
            heldDate = datetime.strptime(heldDate, "%Y-%m-%d %H:%M:%S")
            today = datetime.now()
            diff = today - heldDate
            # 将时间差转换为小时
            diff_hours = diff.total_seconds() / 3600
            if diff_hours > 36:
                lineArr.append("Border开箱查验36个小时不Cleared")
    return lineArr


def read_config():
    with open("config.json", "r", encoding="utf-8") as f:
        return json.load(f)


def pushDaDan(ordersData):
    # 处理数据 只要 ref 和 stats
    data = []
    # 遍历
    for ref, orderData in ordersData.items():

        # 判断status 是否存在不存在就跳过
        if "status" not in orderData["data"]:
            continue

        data.append(
            {
                "ref": ref,
                "status": orderData["data"]["status"],
            }
        )
    # prints(data)
    prints("推送数据中...")
    daDanClient = daDanUpdate()
    r = daDanClient.setTrackStatus(data)
    prints(r)


def main():
    db_mysql = MySQLDatabase()

    connection_result = db_mysql.connection()
    # prints(connection_result)
    # 判断是否连接成功
    if isinstance(connection_result, dict) and connection_result["code"] == 0:
        prints(connection_result["msg"])
        return

    # 计算耗时
    start_time = time.time()

    # 删除temp文件夹下的所有文件
    for root, dirs, files in os.walk("temp"):
        for name in files:
            os.remove(os.path.join(root, name))

    config = {
        "key": "tssTest",
        "getDataListUrl": "http://api.scoa.fbatoll.com/k/httpDB.php",
        "getDataListUrlTimeOut": 10,
        "stopCompany": ["test", "4px"],
    }
    db_oa = DBOA(config)

    # 获取最新的数据
    db_oa.run()

    # 获取后端轨迹数据 快递数据
    ordersData = main_getAUdata()

    # 设置缓存数据
    setCache(db_mysql, ordersData)

    # push数据
    pushData(db_oa)

    # pushOA数据
    pushDaDan(ordersData)

    # 输出本次花费多少 s
    prints(f"本次花费时间：{time.time() - start_time}")


def process_timer():
    config = read_config()
    # 是否直接启动
    if config["start"] == 1:
        prints(f"注意本次是直接启动程序~{process_timer.__name__}")
        main()
    else:
        while True:
            config = read_config()
            now = str(time.strftime("%H:%M:%S", time.localtime()))
            if now in config["startDate"]:
                print(
                    "—Starting | "
                    + str(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
                    + "——————————————————",
                    end="\r",
                    flush=True,
                )
                t = threading.Thread(target=main)
                t.start()
                # 跳出循环
                # break
            else:
                print(
                    "—Await... | "
                    + str(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
                    + "——————————————————",
                    end="\r",
                    flush=True,
                )
            time.sleep(1)


if __name__ == "__main__":
    process_timer()

    # 纠正数据
    # config = {
    #     "key": "tssTest",
    #     "getDataListUrl": "http://api.scoa.fbatoll.com/k/httpDB.php",
    #     "getDataListUrlTimeOut": 10,
    #     "stopCompany": ["test", "4px"],
    # }
    # db_oa = DBOA(config)

    # # 获取 转单号可能异常
    # c, conn = connect_sqlite()
    # c.execute("SELECT connote FROM track")
    # dataList = c.fetchall()
    # # 设置状态
    # signidArr = []
    # for item in dataList:
    #     signidArr.append(item[0])
    # signCode = "0"
    # prints("设置无标识:" + str(len(signidArr)))
    # db_oa.setSignidMax(signidArr, signCode)

    # pushData(db_oa)
